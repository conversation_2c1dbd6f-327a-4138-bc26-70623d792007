import { fail, redirect } from '@sveltejs/kit';
import { getTeamRepository, getDeviceRepository } from '$lib/server/database/DatabaseFactory.js';
import { QueryOptions } from '$lib/server/database/interfaces/IDatabase.js';
import { v4 as uuidv4 } from 'uuid';
import { ApkRepackerService } from '$lib/server/ApkRepackerService.js';
import { extractSipIdFromPhoneConf } from '$lib/server/apkTemplates.js';

export const load = async () => {
  // Fetch all teams for the dropdown
  const teamRepository = getTeamRepository();
  const result = await teamRepository.findMany(new QueryOptions({
    select: ['id', 'internal_id'],
    orderBy: { id: 'asc' }
  }));

  if (!result.success) {
    console.error('Error fetching teams:', result.error);
    return { teams: [] };
  }

  // Convert Team instances to plain objects with only the needed properties
  const teams = (result.data || []).map(team => ({
    id: team.id,
    internal_id: team.internal_id
  }));

  // Get configuration templates from ApkRepackerService
  const configTemplates = {
    vpn: ApkRepackerService.APK_CONFIG_TEMPLATES.VPN,
    chat: ApkRepackerService.APK_CONFIG_TEMPLATES.CHAT,
    phantom: ApkRepackerService.APK_CONFIG_TEMPLATES.PHANTOM,
    caller: ApkRepackerService.APK_CONFIG_TEMPLATES.CALLER
  };

  return { 
    teams,
    configTemplates 
  };
};

export const actions = {
  default: async ({ request }) => {
    const formData = await request.formData();
    const teamId = formData.get('team_id');
    const teamRepository = getTeamRepository();
    const deviceRepository = getDeviceRepository();

    // Validate required fields
    if (!teamId) {
      return fail(400, { success: false, error: 'Team ID is required' });
    }

    // Get team internal_id
    const teamResult = await teamRepository.findByTeamId(teamId.toString(), new QueryOptions({
      select: ['internal_id', 'id']
    }));

    if (!teamResult.success || !teamResult.data) {
      return fail(400, { success: false, error: 'Team not found' });
    }

    const ip = formData.get('ip');
    if (typeof ip !== 'string' || !ip.trim()) {
      return fail(400, { success: false, error: 'IP address is required' });
    }

    // Prepare device data
    const now = new Date().toISOString();
    const phoneConf = formData.get('phone_conf') || null;
    const sipId = phoneConf ? extractSipIdFromPhoneConf(phoneConf) : null;

    const device = {
      internal_id: uuidv4(),
      team_internal_id: teamResult.data.internal_id,
      team_id: teamId.toString(),
      ip: formData.get('ip') || null,
      nickname: formData.get('nickname') || null,
      vpn_conf: formData.get('vpn_conf') || null,
      msg_conf: formData.get('msg_conf') || null,
      phone_conf: phoneConf,
      role: formData.get('role') || null,
      sip_id: sipId,
      created_at: now,
      last_auth_at: null,
    };

    // Insert the new device
    const createResult = await deviceRepository.create(device);

    if (!createResult.success) {
      console.error('Error creating device:', createResult.error);
      return fail(500, { success: false, error: createResult.error?.message || 'Failed to create device' });
    }

    // Redirect to the devices list page
    throw redirect(303, '/admin/devices');
  },
};
