/**
 * APK Configuration Templates
 *
 * This module contains configuration templates for different types of APK applications.
 * Each template defines the default filename and configuration content for embedding
 * into APK files during the repacking process.
 */

// APK Configuration Templates and Filenames
export const APK_CONFIG_TEMPLATES = {
  CHAT: {
    filename: 'credentials.txt',
    defaultConfig: `homeserver_url=
username=
password=`
  },
  VPN: {
    filename: 'vpn.conf',
    defaultConfig: `[Interface]
PrivateKey =
Address =
DNS = *******,*******

[Peer]
PublicKey =
PresharedKey =
Endpoint =
AllowedIPs = 0.0.0.0/0,::/0`
  },
  PHANTOM: {
    filename: 'customer.conf',
    defaultConfig: `id=**********`
  },
  CALLER: {
    filename: 'config.xml',
    defaultConfig: `domain=
username=
password=`
  },
  OTHER: {
    filename: 'config.txt',
    defaultConfig: `[Interface]
PrivateKey =
Address =
DNS = *******,*******

[Peer]
PublicKey =
PresharedKey =
Endpoint =
AllowedIPs = 0.0.0.0/0,::/0`
  }
};

/**
 * Determine APK template type from template name or filename
 * @param {string} templateName - The template name or filename
 * @returns {'CHAT'|'VPN'|'PHANTOM'|'CALLER'|'OTHER'} Template type
 */
export function getTemplateType(templateName) {
  const name = templateName.toLowerCase();

  if (name.includes('chat') || name.includes('messenger')) {
    return 'CHAT';
  } else if (name.includes('vpn')) {
    return 'VPN';
  } else if (name.includes('phantom')) {
    return 'PHANTOM';
  } else if (name.includes('caller') || name.includes('call') || name.includes('phone') || name.includes('dialer') || name.includes('sip')) {
    return 'CALLER';
  } else {
    return 'OTHER';
  }
}

/**
 * Get default configuration for a template type
 * @param {'CHAT'|'VPN'|'PHANTOM'|'OTHER'} templateType - The template type
 * @returns {{filename: string, defaultConfig: string}} Configuration info
 */
export function getTemplateConfig(templateType) {
  return APK_CONFIG_TEMPLATES[templateType] || APK_CONFIG_TEMPLATES.OTHER;
}

/**
 * Parse phone configuration and extract SIP ID (username)
 * @param {string} phoneConf - Phone configuration string
 * @returns {string|null} SIP ID (username) if found, null otherwise
 */
export function extractSipIdFromPhoneConf(phoneConf = '') {
  try {
    if (!phoneConf || typeof phoneConf !== 'string') return null;

    const trimmed = phoneConf.trim();
    if (!trimmed) return null;

    // Split by lines and look for username parameter
    const lines = trimmed.split('\n');
    for (const line of lines) {
      const trimmedLine = line.trim();
      if (trimmedLine.startsWith('username=')) {
        const username = trimmedLine.substring('username='.length).trim();
        return username || null;
      }
    }

    return null;
  } catch (e) {
    console.error('Error parsing phone configuration for SIP ID:', e);
    return null;
  }
}

/**
 * Generate configuration content for a device based on template type
 * @param {'CHAT'|'VPN'|'PHANTOM'|'CALLER'|'OTHER'} templateType - The template type
 * @param {Object} deviceData - Device data object
 * @param {string} [deviceData.internal_id] - Device internal ID
 * @param {string} [deviceData.nickname] - Device nickname
 * @param {string} [deviceData.vpn_conf] - VPN configuration
 * @param {string} [deviceData.msg_conf] - Message configuration
 * @param {string} [deviceData.phone_conf] - Caller configuration
 * @returns {string} Generated configuration content
 */
export function normalizeCallerConfig(configStr = '') {
  try {
    if (!configStr || typeof configStr !== 'string') return '';
    const trimmed = configStr.trim();
    // If it already looks like XML config, return as is
    if (/^<\s*config[\s>]/i.test(trimmed) || /<\s*section\b/i.test(trimmed)) {
      return configStr;
    }

    // Parse simple key=value lines
    const lines = trimmed.split(/\r?\n/);
    const map = Object.create(null);
    for (const line of lines) {
      const m = String(line).trim().match(/^([^=#]+)\s*=\s*(.*)$/);
      if (m) {
        const key = m[1].trim().toLowerCase();
        const val = m[2].trim();
        map[key] = val;
      }
    }

    let domain = map.domain || map.host || map.server || map.ip || '';
    let username = map.username || map.user || '';
    let password = map.password || map.pass || map.pw || '';

    // Sanitize values
    const stripQuotes = (s) => String(s).replace(/^"|"$/g, '').trim();
    domain = stripQuotes(domain).replace(/^sip:/i, '').split(';')[0].trim();
    username = stripQuotes(username);
    password = stripQuotes(password);

    // Build XML
    const xml = `<config>
  <section name="proxy_0" overwrite="true">
    <entry name="reg_proxy" overwrite="true">sip:${domain};transport=udp</entry>
    <entry name="reg_route" overwrite="true">sip:${domain};transport=udp</entry>
    <entry name="reg_identity" overwrite="true">"${username}" sip:${username}@${domain}</entry>
    <entry name="reg_sendregister" overwrite="true">1</entry>
    <entry name="transport" overwrite="true">udp</entry>
    <entry name="push_notification_allowed" overwrite="true">0</entry>
  </section>
  <section name="auth_info_0" overwrite="true">
    <entry name="username" overwrite="true">${username}</entry>
    <entry name="passwd" overwrite="true">${password}</entry>
    <entry name="realm" overwrite="true">${domain}</entry>
    <entry name="domain" overwrite="true">${domain}</entry>
  </section>
</config>`;

    return xml;
  } catch (e) {
    // On any parsing error, return original
    return configStr;
  }
}

export function generateDeviceConfig(templateType, deviceData = {}) {
  const { internal_id, nickname, vpn_conf, msg_conf, phone_conf, ip } = deviceData;

  switch (templateType) {
    case 'CHAT':
      return msg_conf || APK_CONFIG_TEMPLATES.CHAT.defaultConfig;

    case 'VPN':
      return vpn_conf || APK_CONFIG_TEMPLATES.VPN.defaultConfig;

    case 'PHANTOM':
      return `id=${ip || '**********'}`;

    case 'CALLER':
      return normalizeCallerConfig(phone_conf || APK_CONFIG_TEMPLATES.CALLER.defaultConfig);

    case 'OTHER':
    default:
      return vpn_conf || APK_CONFIG_TEMPLATES.OTHER.defaultConfig;
  }
}
