/**
 * Device entity model
 * Represents a device in the system with team relationships and authentication tracking
 */
export class Device {
  /**
   * @param {Object} data - Device data
   * @param {string} data.internal_id - Internal UUID
   * @param {Date} data.created_at - Creation timestamp
   * @param {string} data.team_internal_id - Team's internal ID
   * @param {string} data.team_id - Team's public ID
   * @param {string} data.ip - Device IP address
   * @param {string} data.role - Device role
   * @param {string} data.nickname - Device nickname
   * @param {Date} data.last_auth_at - Last authentication timestamp
   * @param {string} data.vpn_conf - VPN configuration
   * @param {string} data.msg_conf - Message configuration
   * @param {string} data.phone_conf - Caller configuration
   * @param {string} data.fcm_token - Firebase Cloud Messaging token
   * @param {string} data.lang - Device language preference
   */
  constructor(data = {}) {
    this.internal_id = data.internal_id;
    this.created_at = data.created_at ? new Date(data.created_at) : null;
    this.team_internal_id = data.team_internal_id;
    this.team_id = data.team_id;
    this.ip = data.ip;
    this.role = data.role;
    this.nickname = data.nickname;
    this.last_auth_at = data.last_auth_at ? new Date(data.last_auth_at) : null;
    this.vpn_conf = data.vpn_conf;
    this.msg_conf = data.msg_conf;
    this.phone_conf = data.phone_conf;
    this.fcm_token = data.fcm_token;
    this.lang = data.lang;
    
    // Related data (populated by joins)
    this.team = data.team || null;
  }

  /**
   * Validate device data
   * @param {Partial<Device>} data - Device data to validate
   * @param {string} operation - Operation type (create, update)
   * @returns {{valid: boolean, errors: string[]}}
   */
  static validate(data, operation = 'create') {
    const errors = [];

    if (operation === 'create') {
      if (!data.team_internal_id || typeof data.team_internal_id !== 'string') {
        errors.push('Team internal ID is required and must be a string');
      }
      
      if (!data.team_id || typeof data.team_id !== 'string') {
        errors.push('Team ID is required and must be a string');
      }
    }

    if (data.ip && !Device.isValidIP(data.ip)) {
      errors.push('IP address format is invalid');
    }

    if (data.nickname && (typeof data.nickname !== 'string' || data.nickname.trim().length === 0)) {
      errors.push('Nickname must be a non-empty string');
    }

    if (data.fcm_token && typeof data.fcm_token !== 'string') {
      errors.push('FCM token must be a string');
    }

    if (data.lang && typeof data.lang !== 'string') {
      errors.push('Language must be a string');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * Validate IP address format
   * @param {string} ip - IP address to validate
   * @returns {boolean}
   */
  static isValidIP(ip) {
    const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
    const ipv6Regex = /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/;
    return ipv4Regex.test(ip) || ipv6Regex.test(ip);
  }

  /**
   * Convert to database format
   * @returns {Object}
   */
  toDb() {
    return {
      internal_id: this.internal_id,
      created_at: this.created_at?.toISOString(),
      team_internal_id: this.team_internal_id,
      team_id: this.team_id,
      ip: this.ip,
      role: this.role,
      nickname: this.nickname,
      last_auth_at: this.last_auth_at?.toISOString(),
      vpn_conf: this.vpn_conf,
      msg_conf: this.msg_conf,
      phone_conf: this.phone_conf,
      fcm_token: this.fcm_token,
      lang: this.lang
    };
  }

  /**
   * Create from database data
   * @param {Object} dbData - Raw database data
   * @returns {Device}
   */
  static fromDb(dbData) {
    return new Device(dbData);
  }

  /**
   * Get device summary for API responses
   * @returns {Object}
   */
  toSummary() {
    return {
      internal_id: this.internal_id,
      ip: this.ip,
      nickname: this.nickname,
      role: this.role,
      team_id: this.team_id,
      last_auth_at: this.last_auth_at?.toISOString(),
      created_at: this.created_at?.toISOString(),
      lang: this.lang
    };
  }

  /**
   * Check if device is recently active
   * @param {number} hoursThreshold - Hours threshold for recent activity
   * @returns {boolean}
   */
  isRecentlyActive(hoursThreshold = 24) {
    if (!this.last_auth_at) return false;
    const threshold = new Date(Date.now() - hoursThreshold * 60 * 60 * 1000);
    return this.last_auth_at > threshold;
  }

  /**
   * Update authentication timestamp
   */
  updateAuthTimestamp() {
    this.last_auth_at = new Date();
  }

  /**
   * Check if device has FCM token for notifications
   * @returns {boolean}
   */
  canReceiveNotifications() {
    return !!(this.fcm_token && this.fcm_token.trim().length > 0);
  }

  /**
   * Update device language preference
   * @param {string} lang - Language code
   */
  updateLanguage(lang) {
    this.lang = lang;
  }
}

/**
 * Device creation data interface
 */
export class CreateDeviceData {
  /**
   * @param {Object} data
   * @param {string} data.team_internal_id - Team's internal ID
   * @param {string} data.team_id - Team's public ID
   * @param {string} data.ip - Device IP address
   * @param {string} data.role - Device role
   * @param {string} data.nickname - Device nickname
   * @param {string} data.vpn_conf - VPN configuration
   * @param {string} data.msg_conf - Message configuration
   * @param {string} data.phone_conf - Caller configuration
   * @param {string} data.fcm_token - FCM token
   * @param {string} data.lang - Device language preference
   */
  constructor(data) {
    this.team_internal_id = data.team_internal_id;
    this.team_id = data.team_id;
    this.ip = data.ip;
    this.role = data.role;
    this.nickname = data.nickname;
    this.vpn_conf = data.vpn_conf;
    this.msg_conf = data.msg_conf;
    this.phone_conf = data.phone_conf;
    this.fcm_token = data.fcm_token;
    this.lang = data.lang;
    this.created_at = new Date();
  }
}

/**
 * Device update data interface
 */
export class UpdateDeviceData {
  /**
   * @param {Object} data
   * @param {string} data.ip - Device IP address
   * @param {string} data.role - Device role
   * @param {string} data.nickname - Device nickname
   * @param {Date} data.last_auth_at - Last authentication timestamp
   * @param {string} data.vpn_conf - VPN configuration
   * @param {string} data.msg_conf - Message configuration
   * @param {string} data.phone_conf - Caller configuration
   * @param {string} data.fcm_token - FCM token
   * @param {string} data.lang - Device language preference
   */
  constructor(data) {
    if (data.ip !== undefined) this.ip = data.ip;
    if (data.role !== undefined) this.role = data.role;
    if (data.nickname !== undefined) this.nickname = data.nickname;
    if (data.last_auth_at !== undefined) this.last_auth_at = data.last_auth_at;
    if (data.vpn_conf !== undefined) this.vpn_conf = data.vpn_conf;
    if (data.msg_conf !== undefined) this.msg_conf = data.msg_conf;
    if (data.phone_conf !== undefined) this.phone_conf = data.phone_conf;
    if (data.fcm_token !== undefined) this.fcm_token = data.fcm_token;
    if (data.lang !== undefined) this.lang = data.lang;
  }
}

/**
 * Device statistics interface
 */
export class DeviceStatistics {
  /**
   * @param {Object} data
   * @param {number} data.totalDevices - Total number of devices
   * @param {number} data.activeDevices - Recently active devices
   * @param {number} data.recentDevices - Recently created devices
   * @param {Device[]} data.lastConnectedDevices - Last connected devices
   */
  constructor(data) {
    this.totalDevices = data.totalDevices || 0;
    this.activeDevices = data.activeDevices || 0;
    this.recentDevices = data.recentDevices || 0;
    this.lastConnectedDevices = data.lastConnectedDevices || [];
  }
}
