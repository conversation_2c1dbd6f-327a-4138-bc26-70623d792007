import { IDeviceRepository } from '../../interfaces/IDeviceRepository.js';
import { QueryResult, QueryOptions } from '../../interfaces/IDatabase.js';
import { Device, DeviceStatistics } from '../../entities/Device.js';

/**
 * Supabase implementation of Device repository
 */
export class SupabaseDeviceRepository extends IDeviceRepository {
  /**
   * @param {import('@supabase/supabase-js').SupabaseClient} supabaseClient
   */
  constructor(supabaseClient) {
    super(null);
    this.supabase = supabaseClient;
  }

  /**
   * Find devices by team ID
   * @param {string} teamId - Team's public ID
   * @param {QueryOptions} options - Query options
   * @returns {Promise<QueryResult>}
   */
  async findByTeamId(teamId, options) {
    try {
      if (!options) {
        options = new QueryOptions();
      }

      let query = this.supabase.from('devices').select('*').eq('team_id', teamId);

      if (options.orderBy && Object.keys(options.orderBy).length > 0) {
        const orderField = Object.keys(options.orderBy)[0];
        const orderDirection = options.orderBy[orderField] === 'desc' ? false : true;
        query = query.order(orderField, { ascending: orderDirection });
      }

      if (options.limit) {
        query = query.limit(options.limit);
      }

      const { data, error, count } = await query;

      if (error) {
        return QueryResult.error(error);
      }

      const devices = data?.map(item => Device.fromDb(item)) || [];
      return QueryResult.success(devices, count || devices.length);
    } catch (error) {
      return QueryResult.error(error);
    }
  }

  /**
   * Find device by IP address
   * @param {string} ip - Device IP address
   * @param {QueryOptions} options - Query options
   * @returns {Promise<QueryResult>}
   */
  async findByIP(ip, options) {
    try {
      if (!options) {
        options = new QueryOptions();
      }

      const { data, error } = await this.supabase
        .from('devices')
        .select('*')
        .eq('ip', ip)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return QueryResult.success(null, 0);
        }
        return QueryResult.error(error);
      }

      const device = Device.fromDb(data);
      return QueryResult.success(device, 1);
    } catch (error) {
      return QueryResult.error(error);
    }
  }

  /**
   * Find devices with team information (joined data)
   * @param {QueryOptions} options - Query options
   * @returns {Promise<QueryResult>}
   */
  async findWithTeamInfo(options) {
    try {
      if (!options) {
        options = new QueryOptions();
      }

      let query = this.supabase
        .from('devices')
        .select(`
          *,
          teams:team_id (
            id,
            balance,
            owner_id,
            created_at
          )
        `);

      if (options.where) {
        Object.entries(options.where).forEach(([key, value]) => {
          query = query.eq(key, value);
        });
      }

      if (options.orderBy && Object.keys(options.orderBy).length > 0) {
        const orderField = Object.keys(options.orderBy)[0];
        const orderDirection = options.orderBy[orderField] === 'desc' ? false : true;
        query = query.order(orderField, { ascending: orderDirection });
      }

      if (options.limit) {
        query = query.limit(options.limit);
      }

      const { data, error, count } = await query;

      if (error) {
        return QueryResult.error(error);
      }

      const devices = data?.map(item => {
        const device = Device.fromDb(item);
        device.team = item.teams;
        return device;
      }) || [];

      return QueryResult.success(devices, count || devices.length);
    } catch (error) {
      return QueryResult.error(error);
    }
  }

  /**
   * Find recently active devices
   * @param {Date} since - Date threshold for recent activity
   * @param {QueryOptions} options - Query options
   * @returns {Promise<QueryResult>}
   */
  async findRecentlyActive(since, options) {
    try {
      if (!options) {
        options = new QueryOptions();
      }

      let query = this.supabase
        .from('devices')
        .select('*')
        .not('last_auth_at', 'is', null)
        .gte('last_auth_at', since.toISOString());

      if (options.orderBy && Object.keys(options.orderBy).length > 0) {
        const orderField = Object.keys(options.orderBy)[0];
        const orderDirection = options.orderBy[orderField] === 'desc' ? false : true;
        query = query.order(orderField, { ascending: orderDirection });
      } else {
        query = query.order('last_auth_at', { ascending: false });
      }

      if (options.limit) {
        query = query.limit(options.limit);
      }

      const { data, error, count } = await query;

      if (error) {
        return QueryResult.error(error);
      }

      const devices = data?.map(item => Device.fromDb(item)) || [];
      return QueryResult.success(devices, count || devices.length);
    } catch (error) {
      return QueryResult.error(error);
    }
  }

  /**
   * Find recently created devices
   * @param {Date} since - Date threshold for recent creation
   * @param {QueryOptions} options - Query options
   * @returns {Promise<QueryResult>}
   */
  async findRecentlyCreated(since, options) {
    try {
      if (!options) {
        options = new QueryOptions();
      }

      let query = this.supabase
        .from('devices')
        .select('*')
        .gte('created_at', since.toISOString());

      if (options.orderBy && Object.keys(options.orderBy).length > 0) {
        const orderField = Object.keys(options.orderBy)[0];
        const orderDirection = options.orderBy[orderField] === 'desc' ? false : true;
        query = query.order(orderField, { ascending: orderDirection });
      } else {
        query = query.order('created_at', { ascending: false });
      }

      if (options.limit) {
        query = query.limit(options.limit);
      }

      const { data, error, count } = await query;

      if (error) {
        return QueryResult.error(error);
      }

      const devices = data?.map(item => Device.fromDb(item)) || [];
      return QueryResult.success(devices, count || devices.length);
    } catch (error) {
      return QueryResult.error(error);
    }
  }

  /**
   * Find devices with FCM tokens (can receive notifications)
   * @param {QueryOptions} options - Query options
   * @returns {Promise<QueryResult>}
   */
  async findWithFCMTokens(options) {
    try {
      if (!options) {
        options = new QueryOptions();
      }

      let query = this.supabase
        .from('devices')
        .select('*')
        .not('fcm_token', 'is', null)
        .neq('fcm_token', '');

      if (options.where) {
        Object.entries(options.where).forEach(([key, value]) => {
          query = query.eq(key, value);
        });
      }

      if (options.limit) {
        query = query.limit(options.limit);
      }

      const { data, error, count } = await query;

      if (error) {
        return QueryResult.error(error);
      }

      const devices = data?.map(item => Device.fromDb(item)) || [];
      return QueryResult.success(devices, count || devices.length);
    } catch (error) {
      return QueryResult.error(error);
    }
  }

  /**
   * Update device authentication timestamp
   * @param {string} deviceId - Device internal ID
   * @param {Date} authTime - Authentication timestamp
   * @returns {Promise<QueryResult>}
   */
  async updateAuthTimestamp(deviceId, authTime = new Date()) {
    try {
      const { data, error } = await this.supabase
        .from('devices')
        .update({ last_auth_at: authTime.toISOString() })
        .eq('internal_id', deviceId)
        .select()
        .single();

      if (error) {
        return QueryResult.error(error);
      }

      const device = Device.fromDb(data);
      return QueryResult.success(device, 1);
    } catch (error) {
      return QueryResult.error(error);
    }
  }

  /**
   * Update device FCM token
   * @param {string} deviceId - Device internal ID
   * @param {string} fcmToken - FCM token
   * @returns {Promise<QueryResult>}
   */
  async updateFCMToken(deviceId, fcmToken) {
    try {
      const { data, error } = await this.supabase
        .from('devices')
        .update({ fcm_token: fcmToken })
        .eq('internal_id', deviceId)
        .select()
        .single();

      if (error) {
        return QueryResult.error(error);
      }

      const device = Device.fromDb(data);
      return QueryResult.success(device, 1);
    } catch (error) {
      return QueryResult.error(error);
    }
  }

  /**
   * Update device FCM token by IP address
   * @param {string} ip - Device IP address
   * @param {string} fcmToken - FCM token
   * @returns {Promise<QueryResult>}
   */
  async updateFCMTokenByIP(ip, fcmToken) {
    try {
      const { data, error } = await this.supabase
        .from('devices')
        .update({ fcm_token: fcmToken })
        .eq('ip', ip)
        .select()
        .single();

      if (error) {
        return QueryResult.error(error);
      }

      const device = Device.fromDb(data);
      return QueryResult.success(device, 1);
    } catch (error) {
      return QueryResult.error(error);
    }
  }

  /**
   * Update device configuration
   * @param {string} deviceId - Device internal ID
   * @param {Object} config - Configuration object
   * @returns {Promise<QueryResult>}
   */
  async updateConfiguration(deviceId, config) {
    try {
      const updateData = {};
      if (config.vpn_conf !== undefined) updateData.vpn_conf = config.vpn_conf;
      if (config.msg_conf !== undefined) updateData.msg_conf = config.msg_conf;
      if (config.phone_conf !== undefined) updateData.phone_conf = config.phone_conf;

      const { data, error } = await this.supabase
        .from('devices')
        .update(updateData)
        .eq('internal_id', deviceId)
        .select()
        .single();

      if (error) {
        return QueryResult.error(error);
      }

      const device = Device.fromDb(data);
      return QueryResult.success(device, 1);
    } catch (error) {
      return QueryResult.error(error);
    }
  }

  /**
   * Update device language preference
   * @param {string} deviceId - Device internal ID
   * @param {string} lang - Language code
   * @returns {Promise<QueryResult>}
   */
  async updateLanguage(deviceId, lang) {
    try {
      const { data, error } = await this.supabase
        .from('devices')
        .update({ lang: lang })
        .eq('internal_id', deviceId)
        .select()
        .single();

      if (error) {
        return QueryResult.error(error);
      }

      const device = Device.fromDb(data);
      return QueryResult.success(device, 1);
    } catch (error) {
      return QueryResult.error(error);
    }
  }

  /**
   * Update device language preference by IP address
   * @param {string} ip - Device IP address
   * @param {string} lang - Language code
   * @returns {Promise<QueryResult>}
   */
  async updateLanguageByIP(ip, lang) {
    try {
      const { data, error } = await this.supabase
        .from('devices')
        .update({ lang: lang })
        .eq('ip', ip)
        .select()
        .single();

      if (error) {
        return QueryResult.error(error);
      }

      const device = Device.fromDb(data);
      return QueryResult.success(device, 1);
    } catch (error) {
      return QueryResult.error(error);
    }
  }

  /**
   * Get device statistics
   * @param {Object} options - Statistics options
   * @returns {Promise<QueryResult>}
   */
  async getStatistics(options = {}) {
    try {
      const since = options.since || new Date(Date.now() - 24 * 60 * 60 * 1000); // 24 hours ago
      const activeHoursThreshold = options.activeHoursThreshold || 24;

      const [
        totalResult,
        activeResult,
        recentResult,
        lastConnectedResult
      ] = await Promise.all([
        this.count(),
        this.findRecentlyActive(new Date(Date.now() - activeHoursThreshold * 60 * 60 * 1000)),
        this.findRecentlyCreated(since),
        this.getLastConnected(new QueryOptions({ limit: 5 }))
      ]);

      if (!totalResult.success) {
        return QueryResult.error(new Error('Failed to fetch device statistics'));
      }

      const statistics = new DeviceStatistics({
        totalDevices: totalResult.count,
        activeDevices: activeResult.success ? activeResult.count : 0,
        recentDevices: recentResult.success ? recentResult.count : 0,
        lastConnectedDevices: lastConnectedResult.success ? lastConnectedResult.data : []
      });

      return QueryResult.success(statistics, 1);
    } catch (error) {
      return QueryResult.error(error);
    }
  }

  /**
   * Get last connected device
   * @param {QueryOptions} options - Query options
   * @returns {Promise<QueryResult>}
   */
  async getLastConnected(options) {
    try {
      if (!options) {
        options = new QueryOptions();
      }

      const { data, error } = await this.supabase
        .from('devices')
        .select('*')
        .not('last_auth_at', 'is', null)
        .order('last_auth_at', { ascending: false })
        .limit(options.limit || 1);

      if (error) {
        return QueryResult.error(error);
      }

      const devices = data?.map(item => Device.fromDb(item)) || [];
      return QueryResult.success(devices, devices.length);
    } catch (error) {
      return QueryResult.error(error);
    }
  }

  /**
   * Standard CRUD operations
   */
  async findById(id, options) {
    try {
      if (!options) {
        options = new QueryOptions();
      }

      const { data, error } = await this.supabase
        .from('devices')
        .select('*')
        .eq('internal_id', id)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return QueryResult.success(null, 0);
        }
        return QueryResult.error(error);
      }

      const device = Device.fromDb(data);
      return QueryResult.success(device, 1);
    } catch (error) {
      return QueryResult.error(error);
    }
  }

  async findMany(options) {
    try {
      if (!options) {
        options = new QueryOptions();
      }

      let query = this.supabase.from('devices').select('*', { count: 'exact' });

      if (options.where) {
        Object.entries(options.where).forEach(([key, value]) => {
          query = query.eq(key, value);
        });
      }

      if (options.orderBy && Object.keys(options.orderBy).length > 0) {
        const orderField = Object.keys(options.orderBy)[0];
        const orderDirection = options.orderBy[orderField] === 'desc' ? false : true;
        query = query.order(orderField, { ascending: orderDirection });
      }

      if (options.limit) {
        query = query.limit(options.limit);
      }

      if (options.offset) {
        query = query.range(options.offset, options.offset + (options.limit || 1000) - 1);
      }

      const { data, error, count } = await query;

      if (error) {
        return QueryResult.error(error);
      }

      const devices = data?.map(item => Device.fromDb(item)) || [];
      return QueryResult.success(devices, count || devices.length);
    } catch (error) {
      return QueryResult.error(error);
    }
  }

  async create(data) {
    try {
      const validation = Device.validate(data, 'create');
      if (!validation.valid) {
        return QueryResult.error(new Error(`Validation failed: ${validation.errors.join(', ')}`));
      }

      const { data: result, error } = await this.supabase
        .from('devices')
        .insert([data])
        .select()
        .single();

      if (error) {
        return QueryResult.error(error);
      }

      const device = Device.fromDb(result);
      return QueryResult.success(device, 1);
    } catch (error) {
      return QueryResult.error(error);
    }
  }

  async updateById(id, data) {
    try {
      const validation = Device.validate(data, 'update');
      if (!validation.valid) {
        return QueryResult.error(new Error(`Validation failed: ${validation.errors.join(', ')}`));
      }

      const { data: result, error } = await this.supabase
        .from('devices')
        .update(data)
        .eq('internal_id', id)
        .select()
        .single();

      if (error) {
        return QueryResult.error(error);
      }

      const device = Device.fromDb(result);
      return QueryResult.success(device, 1);
    } catch (error) {
      return QueryResult.error(error);
    }
  }

  async deleteById(id) {
    try {
      const { error } = await this.supabase
        .from('devices')
        .delete()
        .eq('internal_id', id);

      if (error) {
        return QueryResult.error(error);
      }

      return QueryResult.success(null, 1);
    } catch (error) {
      return QueryResult.error(error);
    }
  }

  async count(options) {
    try {
      if (!options) {
        options = new QueryOptions();
      }

      let query = this.supabase.from('devices').select('*', { count: 'exact', head: true });

      if (options.where) {
        Object.entries(options.where).forEach(([key, value]) => {
          query = query.eq(key, value);
        });
      }

      const { count, error } = await query;

      if (error) {
        return QueryResult.error(error);
      }

      return QueryResult.success([], count || 0);
    } catch (error) {
      return QueryResult.error(error);
    }
  }

  // Additional required methods
  async findOne(options) {
    if (!options) {
      options = new QueryOptions();
    }

    const result = await this.findMany({ ...options, limit: 1 });
    if (result.success && result.data.length > 0) {
      return QueryResult.success(result.data[0], 1);
    }
    return QueryResult.success(null, 0);
  }

  async createMany(data) {
    try {
      const { data: result, error } = await this.supabase
        .from('devices')
        .insert(data)
        .select();

      if (error) {
        return QueryResult.error(error);
      }

      const devices = result?.map(item => Device.fromDb(item)) || [];
      return QueryResult.success(devices, devices.length);
    } catch (error) {
      return QueryResult.error(error);
    }
  }

  async updateMany(options, data) {
    try {
      let query = this.supabase.from('devices').update(data);

      if (options.where) {
        Object.entries(options.where).forEach(([key, value]) => {
          query = query.eq(key, value);
        });
      }

      const { data: result, error } = await query.select();

      if (error) {
        return QueryResult.error(error);
      }

      const devices = result?.map(item => Device.fromDb(item)) || [];
      return QueryResult.success(devices, devices.length);
    } catch (error) {
      return QueryResult.error(error);
    }
  }

  async deleteMany(options) {
    try {
      let query = this.supabase.from('devices').delete();

      if (options.where) {
        Object.entries(options.where).forEach(([key, value]) => {
          query = query.eq(key, value);
        });
      }

      const { error } = await query;

      if (error) {
        return QueryResult.error(error);
      }

      return QueryResult.success(null, 1);
    } catch (error) {
      return QueryResult.error(error);
    }
  }

  async executeCustomQuery(queryName, params = {}) {
    throw new Error(`Custom query '${queryName}' not implemented`);
  }

  /**
   * Find devices by nickname pattern
   * @param {string} pattern - Nickname search pattern
   * @param {QueryOptions} options - Query options
   * @returns {Promise<QueryResult>}
   */
  async findByNickname(pattern, options) {
    try {
      if (!options) {
        options = new QueryOptions();
      }

      let query = this.supabase.from('devices').select('*').ilike('nickname', `%${pattern}%`);

      if (options.where) {
        Object.entries(options.where).forEach(([key, value]) => {
          query = query.eq(key, value);
        });
      }

      if (options.orderBy && Object.keys(options.orderBy).length > 0) {
        const orderField = Object.keys(options.orderBy)[0];
        const orderDirection = options.orderBy[orderField] === 'desc' ? false : true;
        query = query.order(orderField, { ascending: orderDirection });
      }

      if (options.limit) {
        query = query.limit(options.limit);
      }

      const { data, error, count } = await query;

      if (error) {
        return QueryResult.error(error);
      }

      const devices = data?.map(item => Device.fromDb(item)) || [];
      return QueryResult.success(devices, count || devices.length);
    } catch (error) {
      return QueryResult.error(error);
    }
  }

  /**
   * Delete devices by team ID
   * @param {string} teamId - Team's public ID
   * @returns {Promise<QueryResult>}
   */
  async deleteByTeamId(teamId) {
    try {
      const { error } = await this.supabase
        .from('devices')
        .delete()
        .eq('team_id', teamId);

      if (error) {
        return QueryResult.error(error);
      }

      return QueryResult.success(null, 1);
    } catch (error) {
      return QueryResult.error(error);
    }
  }

  /**
   * Get table name for this repository
   * @returns {string}
   */
  getTableName() {
    return 'devices';
  }

  /**
   * Get device language distribution (count by lang)
   * @returns {Promise<QueryResult>}
   */
  async getLanguageDistribution() {
    try {
      const { data, error } = await this.supabase
        .from('devices')
        .select('lang');
      if (error) {
        return QueryResult.error(error);
      }
      const langCounts = {};
      (data || []).forEach(d => {
        const lang = d.lang || 'unknown';
        langCounts[lang] = (langCounts[lang] || 0) + 1;
      });
      return QueryResult.success(langCounts, Object.keys(langCounts).length);
    } catch (error) {
      return QueryResult.error(error);
    }
  }
}
